# docker-compose.yml (Versión de Producción Recomendada)
version: '3.8'

services:
  mongo:
    image: mongo:8.0.9
    container_name: mongodb
    restart: always
    ports:
      # Expuesto solo a la máquina local (localhost) para más seguridad
      - '127.0.0.1:27017:27017'
    volumes:
      - mongo-data:/data/db
    environment:
      # Las credenciales se leen desde el archivo .env
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_USER}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
    networks:
      - servicios-backend

  elasticsearch:
    image: elasticsearch:8.14.0
    container_name: elasticsearch
    restart: always
    environment:
      - discovery.type=single-node
      - 'ES_JAVA_OPTS=-Xms512m -Xmx512m'
      # Habilitamos la seguridad y leemos la clave desde el archivo .env
      - xpack.security.enabled=true
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD}
    ports:
      # Expuesto solo a la máquina local (localhost)
      - '127.0.0.1:9200:9200'
    volumes:
      - es-data:/usr/share/elasticsearch/data
    networks:
      - servicios-backend
    healthcheck:
      test:
        [
          'CMD-SHELL',
          "curl -s --cacert /usr/share/elasticsearch/config/certs/http_ca.crt -u elastic:${ELASTIC_PASSWORD} https://localhost:9200 | grep -q 'cluster_name'",
        ]
      interval: 10s
      timeout: 10s
      retries: 5

# Definimos una red dedicada para que los servicios se comuniquen entre sí
networks:
  servicios-backend:
    driver: bridge

volumes:
  mongo-data:
  es-data:

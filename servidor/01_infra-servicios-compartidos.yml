# ===================================================================
# SECCIÓN 1: Creación del Namespace para Servicios Compartidos
# Define un espacio de trabajo lógico aislado para registrar los servicios
# que usarán múltiples aplicaciones.
# ===================================================================

apiVersion: v1
kind: Namespace
metadata:
  name: shared-services

---
# ===================================================================
# SECCIÓN 2: Registro de MongoDB como un Servicio Interno
# "Informa" a Kubernetes sobre la existencia de MongoDB.
# ===================================================================

# Objeto 1: El Service. Actúa como un nombre DNS interno.
apiVersion: v1
kind: Service
metadata:
  name: mongodb-external
  namespace: shared-services # <-- Lo creamos dentro del namespace compartido
spec:
  ports:
    - protocol: TCP
      port: 27017
      targetPort: 27017

---
# Objeto 2: El Endpoints. Apunta el nombre DNS a la IP real.
apiVersion: v1
kind: Endpoints
metadata:
  name: mongodb-external # Mismo nombre que el Service
  namespace: shared-services # <-- En el mismo namespace
subsets:
  - addresses:
      # La IP del host en la red de Docker que obtuviste.
      - ip: '**********'
    ports:
      - port: 27017

---
# ===================================================================
# SECCIÓN 3: Registro de Elasticsearch como un Servicio Interno
# "Informa" a Kubernetes sobre la existencia de Elasticsearch.
# ===================================================================

# Objeto 1: El Service.
apiVersion: v1
kind: Service
metadata:
  name: elasticsearch-external
  namespace: shared-services # <-- Lo creamos dentro del namespace compartido
spec:
  ports:
    - protocol: TCP
      port: 9200
      targetPort: 9200

---
# Objeto 2: El Endpoints.
apiVersion: v1
kind: Endpoints
metadata:
  name: elasticsearch-external # Mismo nombre que el Service
  namespace: shared-services # <-- En el mismo namespace
subsets:
  - addresses:
      # La IP del host en la red de Docker que obtuviste.
      - ip: '**********'
    ports:
      - port: 9200

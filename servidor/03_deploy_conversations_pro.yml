# Archivo: forconversations-despliegue.yml

# ===================================================================
# SECCIÓN 1: Namespace para la aplicación 'forconversations'
# Aísla todos los recursos de esta aplicación.
# ===================================================================
apiVersion: v1
kind: Namespace
metadata:
  name: forconversations-prod

---
# ===================================================================
# SECCIÓN 2: Despliegue de la aplicación JHipster
# Define cómo debe correr tu aplicación.
# ===================================================================
apiVersion: apps/v1
kind: Deployment
metadata:
  name: forconversations-deployment
  namespace: forconversations-prod
spec:
  replicas: 1 # Para producción, podrías aumentarlo a 2 para alta disponibilidad
  selector:
    matchLabels:
      app: forconversations
  template:
    metadata:
      labels:
        app: forconversations
    spec:
      containers:
      - name: forconversations-app
        # ¡IMPORTANTE! Reemplaza esto con tu imagen real
        image: tu_usuario/forconversations:1.0.0
        ports:
        - name: http
          containerPort: 8080 # Puerto estándar de las apps JHipster
        env:
        # Apuntamos a los servicios en el namespace 'shared-services'
        - name: SPRING_DATA_MONGODB_URI
          value: "mongodb://wishforthecure:<EMAIL>:27017/forconversations?authSource=admin"
        - name: SPRING_ELASTICSEARCH_URIS
          value: "http://elasticsearch-external.shared-services.svc.cluster.local:9200"
        # ¡CRÍTICO PARA PRODUCCIÓN! Define perfiles y límites de memoria de Java
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: JAVA_OPTS
          value: "-Xmx512m -Xms256m"
        resources:
          # Reserva recursos para asegurar que la app funcione
          requests:
            memory: "512Mi"
            cpu: "250m"
          # Limita los recursos para que no consuma toda la máquina
          limits:
            memory: "1Gi"
            cpu: "1000m"
        # ¡CRÍTICO PARA PRODUCCIÓN! Kubernetes comprueba si tu app está viva y lista
        readinessProbe:
          httpGet:
            path: /api/management/health
            port: http
          initialDelaySeconds: 20
          periodSeconds: 15
        livenessProbe:
          httpGet:
            path: /api/management/health
            port: http
          initialDelaySeconds: 120
          periodSeconds: 30

---
# ===================================================================
# SECCIÓN 3: Service (Exposición interna)
# Crea un nombre DNS interno y estable para tu aplicación.
# ===================================================================
apiVersion: v1
kind: Service
metadata:
  name: forconversations-svc
  namespace: forconversations-prod
spec:
  selector:
    app: forconversations
  ports:
  - name: http
    protocol: TCP
    port: 80 # El servicio escucha en el puerto 80
    targetPort: http # y redirige al puerto 'http' (8080) del contenedor

---
# ===================================================================
# SECCIÓN 4: Ingress (Exposición externa profesional)
# Expone tu aplicación al mundo exterior a través del Ingress Controller.
# ===================================================================
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: forconversations-ingress
  namespace: forconversations-prod
  annotations:
    # Anotación necesaria para que el Ingress Controller de NGINX lo gestione
    kubernetes.io/ingress.class: "nginx"
spec:
  rules:
  # ¡IMPORTANTE! Reemplaza esto con tu dominio real
  - host: "forconversations.tu-dominio.com"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            # Apunta al Service que creamos en la sección anterior
            name: forconversations-svc
            port:
              name: http
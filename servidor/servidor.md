### Crear imagen Docker

```bash
# Package
mvn clean package -Pprod
# Build
docker build -t wishforthecure/forconversations:250613-BETA .
# Push
docker push wishforthecure/forconversations:250613-BETA
```

### Tunel a Mongo

```bash
# port-forward
ssh -N -L 27017:127.0.0.1:27017 root@**************
# Conexion
*******************************************************************************************
```

### 🧠 Arquitectura Recomendada

1.  **Bases de Datos (Mongo/Elastic) fuera de Kubernetes:** Se ejecutan en el propio host de Ubuntu usando **Docker Compose**. Esto te da:

    - **Eficiencia:** Consumen recursos directamente del SO, sin la capa de abstracción de Kubernetes.
    - **Estabilidad:** No se verán afectados por reinicios de pods o problemas en el clúster.
    - **Persistencia Sencilla:** Gestionar los volúmenes de datos es mucho más simple.

2.  **Aplicaciones (Spring Boot) dentro de Kubernetes:** Tus aplicaciones "stateless" (sin estado) se ejecutan como Pods en Kubernetes para aprovechar sus ventajas:
    - **Despliegue y Escalado Fácil:** Gestionas tus apps con `Deployments`.
    - **Auto-reparación:** Si una app falla, Kubernetes la reinicia automáticamente.
    - **Organización:** Usas `Namespaces` para separar tus distintas aplicaciones.

Gráficamente, se vería así:

```
+-------------------------------------------------------------+
| Tu Servidor VPS (Ubuntu 24.04)                              |
|                                                             |
| +-----------------+  <-- Conexión -->  +------------------+ |
| | Docker Compose  |      (Red Local)     | Kubernetes       | |
| | - MongoDB       |                      | - Namespace 'app-1'| |
| | - Elasticsearch |                      |   - Pod Spring   | |
| +-----------------+                      | - Namespace 'app-2'| |
|                                          |   - ...          | |
|                                          +------------------+ |
+-------------------------------------------------------------+
```

---

### ⚙️ Paso 1: Montar MongoDB y Elasticsearch con Docker Compose

Conéctate por SSH a tu servidor Ubuntu. Crea un archivo llamado `docker-compose-services-prod.yml`.

```bash
scp /Users/<USER>/02_laboral/wish_for_the_cure/conversations/250608/servidor/docker-compose-services-prod.yml root@**************:/root/docker-compose/
```

```yaml
# docker-compose-services-prod.yml
version: '3.8'

services:
  mongo:
    image: mongo:6.0
    container_name: mongodb
    restart: always
    ports:
      - '27017:27017' # Exponer solo si necesitas acceso externo directo
    volumes:
      - mongo-data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=tu_usuario_admin
      - MONGO_INITDB_ROOT_PASSWORD=tu_clave_muy_segura

  elasticsearch:
    image: elasticsearch:8.14.0
    container_name: elasticsearch
    restart: always
    environment:
      - discovery.type=single-node
      - 'ES_JAVA_OPTS=-Xms512m -Xmx512m' # ¡CRÍTICO! Limita el uso de RAM
      - xpack.security.enabled=false # Simplificado para empezar. Para producción real, se necesita seguridad.
    ports:
      - '9200:9200'
    volumes:
      - es-data:/usr/share/elasticsearch/data

volumes:
  mongo-data:
  es-data:
```

**Para levantar los servicios:**

```bash
# Instala Docker Compose si no lo tienes
sudo apt-get update && sudo apt-get install docker-compose -y

# Levanta los contenedores en segundo plano
docker-compose up -d

# Verifica que están corriendo
docker-compose ps
```

Ahora tienes MongoDB y Elasticsearch corriendo de forma estable y persistente en tu host.

---

### 🚀 Paso 2: Desplegar tu Aplicación Spring Boot en Kubernetes

Ahora, la parte de Kubernetes. Vamos a crear un `namespace` para tu primera aplicación y la desplegaremos, conectándola a las bases de datos que acabamos de levantar.

**1. Estrategia de Namespaces**

Para tu caso, la mejor estrategia es **un namespace por cada aplicación o microservicio**. Esto mantiene todo ordenado y aislado.

```bash
# Crear un namespace para tu primera aplicación
kubectl create namespace app-springboot-1
```

**2. Conectar Kubernetes con los servicios del Host**

Para que tus Pods puedan "ver" a MongoDB y Elasticsearch, crearemos un `Service` sin selector y un `Endpoint` que apunte a la IP del host en la red de Docker.

Primero, busca la IP del host en la interfaz de Docker:

```bash
ip -4 addr show docker0 | grep -oP 'inet \K[\d.]+'
```

Esto te dará una IP como `**********`. **Usa esa IP en el siguiente archivo YAML.**

Crea el archivo `external-services.yml`:

```bash
scp /Users/<USER>/02_laboral/wish_for_the_cure/conversations/250608/servidor/01_infra-servicios-compartidos.yml root@**************:/root/k8s/
```

```yaml
# external-services.yml
# ===================================================================
# SECCIÓN 1: Creación del Namespace para Servicios Compartidos
# Define un espacio de trabajo lógico aislado para registrar los servicios
# que usarán múltiples aplicaciones.
# ===================================================================

apiVersion: v1
kind: Namespace
metadata:
  name: shared-services

---
# ===================================================================
# SECCIÓN 2: Registro de MongoDB como un Servicio Interno
# "Informa" a Kubernetes sobre la existencia de MongoDB.
# ===================================================================

# Objeto 1: El Service. Actúa como un nombre DNS interno.
apiVersion: v1
kind: Service
metadata:
  name: mongodb-external
  namespace: shared-services # <-- Lo creamos dentro del namespace compartido
spec:
  ports:
    - protocol: TCP
      port: 27017
      targetPort: 27017

---
# Objeto 2: El Endpoints. Apunta el nombre DNS a la IP real.
apiVersion: v1
kind: Endpoints
metadata:
  name: mongodb-external # Mismo nombre que el Service
  namespace: shared-services # <-- En el mismo namespace
subsets:
  - addresses:
      # La IP del host en la red de Docker que obtuviste.
      - ip: '**********'
    ports:
      - port: 27017

---
# ===================================================================
# SECCIÓN 3: Registro de Elasticsearch como un Servicio Interno
# "Informa" a Kubernetes sobre la existencia de Elasticsearch.
# ===================================================================

# Objeto 1: El Service.
apiVersion: v1
kind: Service
metadata:
  name: elasticsearch-external
  namespace: shared-services # <-- Lo creamos dentro del namespace compartido
spec:
  ports:
    - protocol: TCP
      port: 9200
      targetPort: 9200

---
# Objeto 2: El Endpoints.
apiVersion: v1
kind: Endpoints
metadata:
  name: elasticsearch-external # Mismo nombre que el Service
  namespace: shared-services # <-- En el mismo namespace
subsets:
  - addresses:
      # La IP del host en la red de Docker que obtuviste.
      - ip: '**********'
    ports:
      - port: 9200
```

Ahora aplica este archivo:

```bash
kubectl apply -f external-services.yml
```

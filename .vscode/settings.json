{"java.compile.nullAnalysis.mode": "automatic", "dbcode.connections": [{"connectionId": "qYY20KWhnc4sJD-grsPjW", "name": "ForConversatoins", "driver": "mongodb", "connectionType": "host", "driverOptions": {"protocol": "mongodb://"}, "host": "127.0.0.1", "port": 27017, "ssl": false, "savePassword": "secretStorage", "database": "forConversations", "readOnly": false, "role": "development", "connectionTimeout": 30}], "java.configuration.updateBuildConfiguration": "automatic", "mdb.presetConnections": [{"name": "Preset Connection", "connectionString": "mongodb://localhost:27017"}]}
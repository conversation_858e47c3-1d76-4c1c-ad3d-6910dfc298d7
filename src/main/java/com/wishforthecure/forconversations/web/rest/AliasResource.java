package com.wishforthecure.forconversations.web.rest;

import com.wishforthecure.forconversations.repository.AliasRepository;
import com.wishforthecure.forconversations.service.AliasService;
import com.wishforthecure.forconversations.service.dto.AliasDTO;
import com.wishforthecure.forconversations.web.rest.errors.BadRequestAlertException;
import com.wishforthecure.forconversations.web.rest.errors.ElasticsearchExceptionMapper;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.reactive.ResponseUtil;

/**
 * REST controller for managing {@link com.wishforthecure.forconversations.domain.Alias}.
 */
@RestController
@RequestMapping("/api/aliases")
public class AliasResource {

    private static final Logger LOG = LoggerFactory.getLogger(AliasResource.class);

    private static final String ENTITY_NAME = "alias";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final AliasService aliasService;

    private final AliasRepository aliasRepository;

    public AliasResource(AliasService aliasService, AliasRepository aliasRepository) {
        this.aliasService = aliasService;
        this.aliasRepository = aliasRepository;
    }

    /**
     * {@code POST  /aliases} : Create a new alias.
     *
     * @param aliasDTO the aliasDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new aliasDTO, or with status {@code 400 (Bad Request)} if the alias has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public Mono<ResponseEntity<AliasDTO>> createAlias(@RequestBody AliasDTO aliasDTO) throws URISyntaxException {
        LOG.debug("REST request to save Alias : {}", aliasDTO);
        if (aliasDTO.getId() != null) {
            throw new BadRequestAlertException("A new alias cannot already have an ID", ENTITY_NAME, "idexists");
        }
        return aliasService
            .save(aliasDTO)
            .map(result -> {
                try {
                    return ResponseEntity.created(new URI("/api/aliases/" + result.getId()))
                        .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, result.getId()))
                        .body(result);
                } catch (URISyntaxException e) {
                    throw new RuntimeException(e);
                }
            });
    }

    /**
     * {@code PUT  /aliases/:id} : Updates an existing alias.
     *
     * @param id the id of the aliasDTO to save.
     * @param aliasDTO the aliasDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated aliasDTO,
     * or with status {@code 400 (Bad Request)} if the aliasDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the aliasDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public Mono<ResponseEntity<AliasDTO>> updateAlias(@PathVariable(required = false) final String id, @RequestBody AliasDTO aliasDTO)
        throws URISyntaxException {
        LOG.debug("REST request to update Alias : {}, {}", id, aliasDTO);
        if (aliasDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, aliasDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        return aliasRepository
            .existsById(id)
            .flatMap(exists -> {
                if (!exists) {
                    return Mono.error(new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound"));
                }

                return aliasService
                    .update(aliasDTO)
                    .switchIfEmpty(Mono.error(new ResponseStatusException(HttpStatus.NOT_FOUND)))
                    .map(result ->
                        ResponseEntity.ok()
                            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, result.getId()))
                            .body(result)
                    );
            });
    }

    /**
     * {@code PATCH  /aliases/:id} : Partial updates given fields of an existing alias, field will ignore if it is null
     *
     * @param id the id of the aliasDTO to save.
     * @param aliasDTO the aliasDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated aliasDTO,
     * or with status {@code 400 (Bad Request)} if the aliasDTO is not valid,
     * or with status {@code 404 (Not Found)} if the aliasDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the aliasDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public Mono<ResponseEntity<AliasDTO>> partialUpdateAlias(
        @PathVariable(required = false) final String id,
        @RequestBody AliasDTO aliasDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Alias partially : {}, {}", id, aliasDTO);
        if (aliasDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, aliasDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        return aliasRepository
            .existsById(id)
            .flatMap(exists -> {
                if (!exists) {
                    return Mono.error(new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound"));
                }

                Mono<AliasDTO> result = aliasService.partialUpdate(aliasDTO);

                return result
                    .switchIfEmpty(Mono.error(new ResponseStatusException(HttpStatus.NOT_FOUND)))
                    .map(res ->
                        ResponseEntity.ok()
                            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, res.getId()))
                            .body(res)
                    );
            });
    }

    /**
     * {@code GET  /aliases} : get all the aliases.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of aliases in body.
     */
    @GetMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<List<AliasDTO>> getAllAliases() {
        LOG.debug("REST request to get all Aliases");
        return aliasService.findAll().collectList();
    }

    /**
     * {@code GET  /aliases} : get all the aliases as a stream.
     * @return the {@link Flux} of aliases.
     */
    @GetMapping(value = "", produces = MediaType.APPLICATION_NDJSON_VALUE)
    public Flux<AliasDTO> getAllAliasesAsStream() {
        LOG.debug("REST request to get all Aliases as a stream");
        return aliasService.findAll();
    }

    /**
     * {@code GET  /aliases/:id} : get the "id" alias.
     *
     * @param id the id of the aliasDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the aliasDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public Mono<ResponseEntity<AliasDTO>> getAlias(@PathVariable String id) {
        LOG.debug("REST request to get Alias : {}", id);
        Mono<AliasDTO> aliasDTO = aliasService.findOne(id);
        return ResponseUtil.wrapOrNotFound(aliasDTO);
    }

    /**
     * {@code DELETE  /aliases/:id} : delete the "id" alias.
     *
     * @param id the id of the aliasDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public Mono<ResponseEntity<Void>> deleteAlias(@PathVariable String id) {
        LOG.debug("REST request to delete Alias : {}", id);
        return aliasService
            .delete(id)
            .then(
                Mono.just(
                    ResponseEntity.noContent().headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id)).build()
                )
            );
    }

    /**
     * {@code SEARCH  /aliases/_search?query=:query} : search for the alias corresponding
     * to the query.
     *
     * @param query the query of the alias search.
     * @return the result of the search.
     */
    @GetMapping("/_search")
    public Mono<List<AliasDTO>> searchAliases(@RequestParam String query) {
        LOG.debug("REST request to search Aliases for query {}", query);
        try {
            return aliasService.search(query).collectList();
        } catch (RuntimeException e) {
            throw ElasticsearchExceptionMapper.mapException(e);
        }
    }
}

package com.wishforthecure.forconversations.service.dto;

import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import com.wishforthecure.forconversations.security.SecurityUtils;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import reactor.core.publisher.Mono;

/**
 * A DTO for the {@link com.wishforthecure.forconversations.domain.Source}
 * entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class SourceDTO implements Serializable {

    private String id;

    @NotNull(message = "must not be null")
    private Instant time;

    private List<String> messagesIds;

    @NotNull(message = "must not be null")
    private SourceType type;

    private byte[] file;

    private String fileContentType;

    public SourceDTO() {
        // Constructor sin argumentos para frameworks
    }

    public SourceDTO(Instant time, List<String> messagesIds, SourceType type, byte[] file, String fileContentType) {
        this.time = time;
        this.messagesIds = messagesIds;
        this.type = type;
        this.file = file;
        this.fileContentType = fileContentType;
    }

    public SourceDTO(Instant time, List<String> messagesIds, SourceType type, byte[] file, String fileContentType, String sourceId) {
        this.time = time;
        this.messagesIds = messagesIds;
        this.type = type;
        this.file = file;
        this.fileContentType = fileContentType;
    }

    public Instant getTime() {
        return time;
    }

    public void setTime(Instant time) {
        this.time = time;
    }

    public List<String> getMessagesIds() {
        return messagesIds;
    }

    public void setMessagesIds(List<String> messagesIds) {
        this.messagesIds = messagesIds;
    }

    public byte[] getFile() {
        return file;
    }

    public void setFile(byte[] file) {
        this.file = file;
    }

    public String getFileContentType() {
        return fileContentType;
    }

    public void setFileContentType(String fileContentType) {
        this.fileContentType = fileContentType;
    }

    public Mono<SourceDTO> withGeneratedIds() {
        return SecurityUtils.getCurrentUserLogin()
            .flatMap(loggedInUser -> {
                String timestamp = String.valueOf(Instant.now().toEpochMilli());
                this.id = "%s_%s_%s".formatted(loggedInUser, timestamp, this.type.toString().toLowerCase());
                this.time = Instant.now();
                return Mono.just(this);
            });
    }

    public SourceType getType() {
        return type;
    }

    public void setType(SourceType type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof SourceDTO)) {
            return false;
        }

        SourceDTO sourceDTO = (SourceDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, sourceDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "SourceDTO{" +
                "id='" + getId() + "'" +
                ", time='" + getTime() + "'" +
                ", messagesIds='" + getMessagesIds() + "'" +
                ", type='" + getType() + "'" +
                ",  file='" + getFile() + "'" +
                ", fileContentType='" + getFileContentType() + "'" +
                "}";
    }
}

package com.wishforthecure.forconversations.domain;

import com.wishforthecure.forconversations.domain.enumeration.SourceType;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * A Source.
 */
@Document(collection = "source")
@org.springframework.data.elasticsearch.annotations.Document(indexName = "source")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Source implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    private String id;

    @NotNull(message = "must not be null")
    @Field("time")
    private Instant time;

    @Field("messages")
    @org.springframework.data.elasticsearch.annotations.Field(type = org.springframework.data.elasticsearch.annotations.FieldType.Text)
    private String messages;

    @Field("messages_ids")
    @org.springframework.data.elasticsearch.annotations.Field(type = org.springframework.data.elasticsearch.annotations.FieldType.Text)
    private List<String> messagesIds;

    @NotNull(message = "must not be null")
    @Field("type")
    @org.springframework.data.elasticsearch.annotations.Field(type = org.springframework.data.elasticsearch.annotations.FieldType.Keyword)
    private SourceType type;

    @Field("file")
    @org.springframework.data.elasticsearch.annotations.Field(type = org.springframework.data.elasticsearch.annotations.FieldType.Binary)
    private byte[] file;

    @Field("file_content_type")
    @org.springframework.data.elasticsearch.annotations.Field(type = org.springframework.data.elasticsearch.annotations.FieldType.Text)
    private String fileContentType;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public String getId() {
        return this.id;
    }

    public Source id(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Instant getTime() {
        return this.time;
    }

    public Source time(Instant time) {
        this.setTime(time);
        return this;
    }

    public void setTime(Instant time) {
        this.time = time;
    }

    public String getMessages() {
        return this.messages;
    }

    public Source messages(String messages) {
        this.setMessages(messages);
        return this;
    }

    public void setMessages(String messages) {
        this.messages = messages;
    }

    public List<String> getMessagesIds() {
        return this.messagesIds;
    }

    public Source messagesIds(List<String> messagesIds) {
        this.setMessagesIds(messagesIds);
        return this;
    }

    public void setMessagesIds(List<String> messagesIds) {
        this.messagesIds = messagesIds;
    }

    public SourceType getType() {
        return this.type;
    }

    public Source type(SourceType type) {
        this.setType(type);
        return this;
    }

    public void setType(SourceType type) {
        this.type = type;
    }

    public byte[] getFile() {
        return this.file;
    }

    public Source file(byte[] file) {
        this.setFile(file);
        return this;
    }

    public void setFile(byte[] file) {
        this.file = file;
    }

    public String getFileContentType() {
        return this.fileContentType;
    }

    public Source fileContentType(String fileContentType) {
        this.setFileContentType(fileContentType);
        return this;
    }

    public void setFileContentType(String fileContentType) {
        this.fileContentType = fileContentType;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and
    // setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Source)) {
            return false;
        }
        return getId() != null && getId().equals(((Source) o).getId());
    }

    @Override
    public int hashCode() {
        // see
        // https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Source{" +
                "id=" + getId() +
                ", time='" + getTime() + "'" +
                ", messages='" + getMessages() + "'" +
                ", type='" + getType() + "'" +
                ", file='" + getFile() + "'" +
                ", fileContentType='" + getFileContentType() + "'" +
                "}";
    }
}
